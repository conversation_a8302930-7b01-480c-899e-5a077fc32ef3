from llama_index.core import SimpleDirectoryReader, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.openai_like import OpenAILike
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# --- 配置 ---
DEEPSEEK_API_KEY = "***********************************"
DATA_DIR = "data"
OUTPUT_FILE = "fast_rag_dataset.jsonl"

print("--- 快速生成微调数据集（使用DeepSeek API）---")

# 1. 加载文档
print(f"步骤 1/3: 从 '{DATA_DIR}' 目录加载文档...")
documents = SimpleDirectoryReader(DATA_DIR, recursive=True).load_data()
print(f"文档加载完成，共 {len(documents)} 个文档。")

# 2. 分割文档
node_parser = SentenceSplitter(chunk_size=512)
nodes = node_parser.get_nodes_from_documents(documents)
print(f"步骤 2/3: 文档分割完成，共生成 {len(nodes)} 个节点。")

# 根据重建后的知识库规模，可以处理更多节点
nodes = nodes[:50]  # 增加到50个节点
print(f"基于重建的知识库，使用前 {len(nodes)} 个节点。")

# 3. 初始化LLM
llm = OpenAILike(
    model="deepseek-chat",
    api_base="https://api.deepseek.com",
    api_key=DEEPSEEK_API_KEY,
    context_window=128000,
    is_chat_model=True,
    is_function_calling_model=False,
    max_tokens=2048,  # 减少token数以加快速度
    temperature=0.3,  # 降低温度以获得更一致的结果
    top_p=0.8,
    timeout=30,  # 减少超时时间
    max_retries=2
)

def generate_qa_pair(node):
    """为单个节点生成问答对"""
    try:
        context = node.text
        
        # 根据文本类型生成不同类型的问题
        if "时间:" in context and "闸前水位" in context:
            # 时序数据类型
            prompt = f"""基于以下水利工程时序数据，生成1个专业问题和对应的答案。

时序数据：
{context}

请按以下格式输出：
问题：[在这里写问题]
答案：[在这里写答案]

问题类型可以包括：
- 数据分析类：最大值、最小值、平均值、变化趋势
- 时间查询类：特定时间点的数据状态
- 比较分析类：不同时间段的数据对比
- 运行状态类：设备运行状态分析"""
        else:
            # 法规文档类型
            prompt = f"""基于以下南水北调工程管理文档，生成1个专业问题和对应的答案。

文档内容：
{context}

请按以下格式输出：
问题：[在这里写问题]
答案：[在这里写答案]

问题类型可以包括：
- 法规解释类：具体条款的含义和适用范围
- 管理流程类：工程管理的具体流程和要求
- 责任义务类：各方的责任和义务
- 技术标准类：技术规范和标准要求"""

        response = llm.complete(prompt)
        response_text = str(response).strip()
        
        # 解析问题和答案
        lines = response_text.split('\n')
        question = ""
        answer = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('问题：') or line.startswith('问题:'):
                question = line.replace('问题：', '').replace('问题:', '').strip()
            elif line.startswith('答案：') or line.startswith('答案:'):
                answer = line.replace('答案：', '').replace('答案:', '').strip()
        
        if question and answer:
            return {
                "query": question,
                "context": context,
                "response": answer
            }
        else:
            # 如果解析失败，使用原始响应
            return {
                "query": f"基于以下内容，请回答相关问题：{context[:100]}...",
                "context": context,
                "response": response_text
            }
            
    except Exception as e:
        print(f"处理节点时出错: {e}")
        return None

# 4. 并发生成问答对
print("步骤 3/3: 开始生成问答对...")
start_time = time.time()

train_data = []
max_workers = 5  # 增加并发数

with ThreadPoolExecutor(max_workers=max_workers) as executor:
    # 提交所有任务
    future_to_node = {
        executor.submit(generate_qa_pair, node): i 
        for i, node in enumerate(nodes)
    }
    
    # 收集结果
    completed = 0
    for future in as_completed(future_to_node):
        result = future.result()
        if result:
            train_data.append(result)
        
        completed += 1
        elapsed = time.time() - start_time
        print(f"  已处理 {completed}/{len(nodes)} (耗时: {elapsed:.1f}秒)")

# 5. 保存结果
try:
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        for item in train_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    total_time = time.time() - start_time
    print(f"\n--- 数据集生成完成！---")
    print(f"文件保存至: {OUTPUT_FILE}")
    print(f"成功生成: {len(train_data)} 条数据")
    print(f"总耗时: {total_time:.1f} 秒")
    print(f"平均每条: {total_time/len(train_data):.1f} 秒")
    
    # 显示示例
    if train_data:
        print("\n--- 数据示例 ---")
        example = train_data[0]
        print(f"问题: {example['query']}")
        print(f"答案: {example['response']}")
        print(f"上下文长度: {len(example['context'])} 字符")
        
except Exception as e:
    print(f"保存文件时出错: {e}")

print("\n提示：如果效果满意，可以增加节点数量来生成更多数据")
