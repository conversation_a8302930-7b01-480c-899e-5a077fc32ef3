from llama_index.core import SimpleDirectoryReader, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.openai_like import OpenAILike
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# --- 配置 ---
DEEPSEEK_API_KEY = "***********************************"
DATA_DIR = "data"
OUTPUT_FILE = "high_quality_rag_dataset_500.jsonl"

print("--- 快速生成微调数据集（使用DeepSeek API）---")

# 1. 加载文档
print(f"步骤 1/3: 从 '{DATA_DIR}' 目录加载文档...")
documents = SimpleDirectoryReader(DATA_DIR, recursive=True).load_data()
print(f"文档加载完成，共 {len(documents)} 个文档。")

# 2. 分割文档
node_parser = SentenceSplitter(chunk_size=512)
nodes = node_parser.get_nodes_from_documents(documents)
print(f"步骤 2/3: 文档分割完成，共生成 {len(nodes)} 个节点。")

# 生成500条高质量数据集，使用所有节点并重复采样
print(f"总共有 {len(nodes)} 个节点，将生成500条高质量数据集")
target_samples = 500

# 如果节点数少于500，我们将重复使用节点但生成不同类型的问题
if len(nodes) < target_samples:
    # 重复节点列表直到达到500个
    extended_nodes = []
    cycles_needed = (target_samples // len(nodes)) + 1
    for cycle in range(cycles_needed):
        extended_nodes.extend(nodes)
    nodes = extended_nodes[:target_samples]
    print(f"扩展节点列表到 {len(nodes)} 个，确保生成500条不同的问答对")

# 3. 初始化LLM
llm = OpenAILike(
    model="deepseek-chat",
    api_base="https://api.deepseek.com",
    api_key=DEEPSEEK_API_KEY,
    context_window=128000,
    is_chat_model=True,
    is_function_calling_model=False,
    max_tokens=2048,  # 减少token数以加快速度
    temperature=0.3,  # 降低温度以获得更一致的结果
    top_p=0.8,
    timeout=30,  # 减少超时时间
    max_retries=2
)

def generate_qa_pair(node_data):
    """为单个节点生成问答对"""
    node, question_type = node_data
    try:
        context = node.text

        # 根据文本类型和问题类型生成不同的问题
        if "时间:" in context and "闸前水位" in context:
            # 时序数据类型 - 根据question_type生成不同类型的问题
            if question_type == "analysis":
                prompt = f"""你是水利工程专家。基于以下闸门运行时序数据，生成一个数据分析类问题和准确答案。

时序数据：
{context}

要求生成数据分析类问题，如：最大值/最小值查询、平均值计算、变化趋势分析、数据对比等。

请按以下格式输出：
问题：[具体的数据分析问题]
答案：[基于数据的准确答案，包含具体数值和单位]"""
            elif question_type == "time":
                prompt = f"""你是水利工程专家。基于以下闸门运行时序数据，生成一个时间查询类问题和准确答案。

时序数据：
{context}

要求生成时间查询类问题，如：特定时间点的运行状态、某个时间段的数据变化、时间相关的运行分析等。

请按以下格式输出：
问题：[具体的时间查询问题]
答案：[基于数据的准确答案，包含时间和对应数值]"""
            elif question_type == "operation":
                prompt = f"""你是水利工程专家。基于以下闸门运行时序数据，生成一个运行状态类问题和准确答案。

时序数据：
{context}

要求生成运行状态类问题，如：闸门开度变化、流量变化规律、水位差分析、运行稳定性等。

请按以下格式输出：
问题：[具体的运行状态问题]
答案：[基于数据的专业分析答案]"""
            else:  # comparison
                prompt = f"""你是水利工程专家。基于以下闸门运行时序数据，生成一个比较分析类问题和准确答案。

时序数据：
{context}

要求生成比较分析类问题，如：不同时间段对比、参数间关系分析、变化幅度比较等。

请按以下格式输出：
问题：[具体的比较分析问题]
答案：[基于数据的对比分析答案]"""
        else:
            # 法规文档类型
            if question_type == "regulation":
                prompt = f"""你是水利工程法规专家。基于以下南水北调工程管理文档，生成一个法规解释类问题和准确答案。

文档内容：
{context}

要求生成法规解释类问题，如：条款含义、适用范围、法律依据、执行标准等。

请按以下格式输出：
问题：[具体的法规解释问题]
答案：[准确的法规解释答案]"""
            elif question_type == "process":
                prompt = f"""你是水利工程管理专家。基于以下南水北调工程管理文档，生成一个管理流程类问题和准确答案。

文档内容：
{context}

要求生成管理流程类问题，如：工作流程、操作程序、管理要求、执行步骤等。

请按以下格式输出：
问题：[具体的管理流程问题]
答案：[详细的流程说明答案]"""
            elif question_type == "responsibility":
                prompt = f"""你是水利工程管理专家。基于以下南水北调工程管理文档，生成一个责任义务类问题和准确答案。

文档内容：
{context}

要求生成责任义务类问题，如：各方职责、义务要求、责任划分、权限范围等。

请按以下格式输出：
问题：[具体的责任义务问题]
答案：[明确的责任义务说明]"""
            else:  # technical
                prompt = f"""你是水利工程技术专家。基于以下南水北调工程管理文档，生成一个技术标准类问题和准确答案。

文档内容：
{context}

要求生成技术标准类问题，如：技术规范、质量标准、安全要求、验收标准等。

请按以下格式输出：
问题：[具体的技术标准问题]
答案：[准确的技术标准说明]"""

        response = llm.complete(prompt)
        response_text = str(response).strip()
        
        # 解析问题和答案
        lines = response_text.split('\n')
        question = ""
        answer = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('问题：') or line.startswith('问题:'):
                question = line.replace('问题：', '').replace('问题:', '').strip()
            elif line.startswith('答案：') or line.startswith('答案:'):
                answer = line.replace('答案：', '').replace('答案:', '').strip()
        
        if question and answer:
            return {
                "query": question,
                "context": context,
                "response": answer
            }
        else:
            # 如果解析失败，使用原始响应
            return {
                "query": f"基于以下内容，请回答相关问题：{context[:100]}...",
                "context": context,
                "response": response_text
            }
            
    except Exception as e:
        print(f"处理节点时出错: {e}")
        return None

# 4. 并发生成500条高质量问答对
print("步骤 3/3: 开始生成500条高质量问答对...")
start_time = time.time()

# 定义问题类型以确保多样性
question_types = {
    "data": ["analysis", "time", "operation", "comparison"],  # 时序数据问题类型
    "doc": ["regulation", "process", "responsibility", "technical"]  # 文档问题类型
}

# 为每个节点分配问题类型，确保多样性
node_with_types = []
for i, node in enumerate(nodes):
    if "时间:" in node.text and "闸前水位" in node.text:
        question_type = question_types["data"][i % 4]
    else:
        question_type = question_types["doc"][i % 4]
    node_with_types.append((node, question_type))

train_data = []
max_workers = 8  # 增加并发数以提高速度

print(f"使用 {max_workers} 个线程并发处理...")

with ThreadPoolExecutor(max_workers=max_workers) as executor:
    # 提交所有任务
    future_to_data = {
        executor.submit(generate_qa_pair, node_data): i
        for i, node_data in enumerate(node_with_types)
    }

    # 收集结果
    completed = 0
    failed = 0
    for future in as_completed(future_to_data):
        result = future.result()
        if result:
            train_data.append(result)
        else:
            failed += 1

        completed += 1
        elapsed = time.time() - start_time
        if completed % 50 == 0 or completed == len(node_with_types):
            success_rate = ((completed - failed) / completed) * 100
            avg_time = elapsed / completed
            eta = (len(node_with_types) - completed) * avg_time
            print(f"  已处理 {completed}/{len(node_with_types)} | 成功率: {success_rate:.1f}% | 预计剩余: {eta/60:.1f}分钟")

# 5. 保存结果
try:
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        for item in train_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    total_time = time.time() - start_time
    success_count = len(train_data)
    target_count = 500

    print(f"\n🎉 --- 高质量数据集生成完成！---")
    print(f"📁 文件保存至: {OUTPUT_FILE}")
    print(f"✅ 成功生成: {success_count}/{target_count} 条数据")
    print(f"📊 成功率: {(success_count/target_count)*100:.1f}%")
    print(f"⏱️ 总耗时: {total_time/60:.1f} 分钟")
    print(f"⚡ 平均每条: {total_time/success_count:.1f} 秒")

    # 分析数据类型分布
    data_types = {"时序数据": 0, "法规文档": 0}
    for item in train_data:
        if "时间:" in item['context'] and "闸前水位" in item['context']:
            data_types["时序数据"] += 1
        else:
            data_types["法规文档"] += 1

    print(f"\n📈 --- 数据类型分布 ---")
    for dtype, count in data_types.items():
        print(f"{dtype}: {count} 条 ({(count/success_count)*100:.1f}%)")

    # 显示示例
    if train_data:
        print(f"\n📝 --- 数据质量示例 ---")
        # 显示时序数据示例
        for item in train_data[:3]:
            if "时间:" in item['context']:
                print(f"【时序数据类】")
                print(f"问题: {item['query'][:80]}...")
                print(f"答案: {item['response'][:80]}...")
                break

        # 显示法规文档示例
        for item in train_data[:10]:
            if "时间:" not in item['context']:
                print(f"\n【法规文档类】")
                print(f"问题: {item['query'][:80]}...")
                print(f"答案: {item['response'][:80]}...")
                break
        
except Exception as e:
    print(f"保存文件时出错: {e}")

print("\n提示：如果效果满意，可以增加节点数量来生成更多数据")
