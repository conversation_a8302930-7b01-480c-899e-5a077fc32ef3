# 使用DeepSeek API生成RAG微调数据集

## 概述
DeepSeek API完全兼容OpenAI格式，可以直接替代OpenAI GPT-4用于生成高质量的RAG微调数据集。相比OpenAI，DeepSeek具有以下优势：

- **性价比高**: 价格比OpenAI便宜很多
- **效果好**: DeepSeek-V3模型在多项基准测试中表现优异
- **支持中文**: 对中文理解和生成能力强
- **大上下文**: 支持128K上下文窗口

## 使用步骤

### 1. 获取DeepSeek API密钥
1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 在控制台中创建API密钥
4. 复制您的API密钥

### 2. 修改配置
在 `generate_dataset_deepseek.py` 文件中，将以下行：
```python
DEEPSEEK_API_KEY = "YOUR_DEEPSEEK_API_KEY"
```
替换为您的真实API密钥：
```python
DEEPSEEK_API_KEY = "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
```

### 3. 准备数据
将您的原始文档（PDF、Excel、Word等）放在 `./data` 目录下。

### 4. 安装依赖
确保安装了必要的依赖包：
```bash
pip install llama-index
pip install llama-index-llms-openai-like
```

### 5. 运行脚本
```bash
python generate_dataset_deepseek.py
```

## 配置参数说明

### DeepSeek模型配置
```python
data_gen_llm = OpenAILike(
    model="deepseek-chat",          # 模型名称
    api_base="https://api.deepseek.com",  # API端点
    api_key=DEEPSEEK_API_KEY,       # 您的API密钥
    context_window=128000,          # 上下文窗口大小
    is_chat_model=True,             # 是否为聊天模型
    is_function_calling_model=False, # 是否支持函数调用
    max_tokens=4096,                # 最大生成token数
    temperature=0.5,                # 温度参数(0-1)
    top_p=0.9,                      # top_p参数(0-1)
    timeout=60,                     # 请求超时时间(秒)
    max_retries=3                   # 重试次数
)
```

### 数据生成配置
```python
qa_dataset = generate_question_context_pairs(
    nodes,
    llm=data_gen_llm,
    num_questions_per_chunk=2,      # 每个文档块生成的问题数量
    show_progress=True              # 显示进度条
)
```

## 输出格式
生成的数据集保存为JSONL格式，每行包含一个JSON对象：
```json
{
    "query": "问题内容",
    "context": "相关上下文",
    "response": "标准答案"
}
```

## 成本估算
DeepSeek API的定价（以下为参考价格，请以官网为准）：
- 输入token: ¥0.001/1K tokens
- 输出token: ¥0.002/1K tokens

对于1000个文档块，大约需要：
- 生成问题: ~500K tokens
- 生成答案: ~1M tokens
- 总成本: 约¥2-5元

## 常见问题

### Q: 如何提高生成质量？
A: 
1. 调整temperature参数（0.3-0.7之间）
2. 优化prompt模板
3. 增加num_questions_per_chunk参数
4. 确保原始文档质量高

### Q: 生成速度慢怎么办？
A: 
1. 减少num_questions_per_chunk参数
2. 增加timeout时间
3. 检查网络连接
4. 考虑分批处理大量数据

### Q: API调用失败怎么办？
A: 
1. 检查API密钥是否正确
2. 确认账户余额充足
3. 检查网络连接
4. 查看错误信息并相应调整

### Q: 如何自定义prompt？
A: 修改生成答案部分的prompt模板：
```python
prompt = f"""
你是一个{领域}专家。请根据以下上下文，{具体要求}。

上下文:
{context}

问题:
{query}

答案:
"""
```

## 与其他API的对比

| API服务 | 价格 | 中文支持 | 上下文长度 | 推荐度 |
|---------|------|----------|------------|--------|
| DeepSeek | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 128K | ⭐⭐⭐⭐⭐ |
| OpenAI GPT-4 | ⭐⭐ | ⭐⭐⭐⭐ | 128K | ⭐⭐⭐⭐ |
| 阿里云千问 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 128K | ⭐⭐⭐⭐ |
| 智谱GLM | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 128K | ⭐⭐⭐ |

## 注意事项
1. 请妥善保管您的API密钥，不要泄露给他人
2. 建议先用少量数据测试，确认效果后再大规模生成
3. 生成的数据质量很大程度上取决于原始文档的质量
4. 建议人工抽查生成的数据，确保质量符合要求
