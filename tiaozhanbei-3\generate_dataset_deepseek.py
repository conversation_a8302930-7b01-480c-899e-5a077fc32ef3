from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.evaluation import generate_question_context_pairs
from llama_index.llms.openai_like import OpenAILike
import json

# --- 配置 ---
# 使用DeepSeek API，性价比高且效果好
# 请将 'YOUR_DEEPSEEK_API_KEY' 替换为您的真实密钥
DEEPSEEK_API_KEY = "YOUR_DEEPSEEK_API_KEY"
DATA_DIR = "./tiaozhanbei-3/data"  # 您存放原始PDF和Excel的目录
OUTPUT_FILE = "rag_finetune_dataset.jsonl"  # 输出的数据集文件

print("--- 开始生成微调数据集（使用DeepSeek API）---")

# 1. 加载您的原始文档
# SimpleDirectoryReader可以方便地加载目录下的所有文件
print(f"步骤 1/4: 从 '{DATA_DIR}' 目录加载文档...")
documents = SimpleDirectoryReader(DATA_DIR, recursive=True).load_data()
print(f"文档加载完成，共 {len(documents)} 个文档。")

# 2. 将文档分割成节点 (Nodes)
# 这里的分割策略应与您RAG系统中的保持一致
node_parser = SentenceSplitter(chunk_size=512)
nodes = node_parser.get_nodes_from_documents(documents)
print(f"步骤 2/4: 文档分割完成，共生成 {len(nodes)} 个节点。")

# 为了测试和控制成本，我们只使用前50个节点
# 如果测试成功，您可以删除下面这行来处理所有节点
nodes = nodes[:50]
print(f"为了测试，只使用前 {len(nodes)} 个节点。")

# 3. 初始化用于生成数据的高质量LLM
# 使用DeepSeek API，性价比高且效果好
data_gen_llm = OpenAILike(
    model="deepseek-chat",  # DeepSeek的聊天模型
    api_base="https://api.deepseek.com",  # DeepSeek API端点
    api_key="***********************************",
    context_window=128000,  # DeepSeek支持128K上下文
    is_chat_model=True,
    is_function_calling_model=False,
    max_tokens=4096,  # 最大生成token数
    temperature=0.5,  # 温度参数
    top_p=0.9,  # top_p参数
    timeout=60,  # 增加超时时间
    max_retries=3  # 重试次数
)
Settings.llm = data_gen_llm
print("步骤 3/4: 初始化DeepSeek数据生成模型完成。")

# 4. 生成"问题-上下文"对
# 这是最关键的一步，LlamaIndex会自动处理与LLM的交互
print("步骤 4/4: 开始生成'问题-上下文'对（此过程可能需要较长时间）...")
try:
    qa_dataset = generate_question_context_pairs(
        nodes,
        llm=data_gen_llm,
        num_questions_per_chunk=2  # 为每个文档块生成2个问题，可以根据需要调整
    )
    print("问题-上下文对生成完成！")
except Exception as e:
    print(f"生成问题-上下文对时出错: {e}")
    print("请检查您的DeepSeek API密钥是否正确，以及网络连接是否正常。")
    exit(1)

# 5. 将生成的数据转换为包含(query, context, response)的格式并保存
# 我们需要再次调用LLM，根据问题和上下文生成一个"标准答案"
train_data = []
total_pairs = len(qa_dataset.queries)
print(f"开始为 {total_pairs} 个问答对生成标准答案...")

for i, (query_id, query) in enumerate(qa_dataset.queries.items()):
    try:
        context_id = qa_dataset.relevant_docs[query_id][0]
        context = qa_dataset.corpus[context_id]

        # 使用DeepSeek生成答案
        prompt = f"""
        你是一个水利专家。请根据以下上下文，准确回答问题。

        上下文:
        {context}

        问题:
        {query}

        答案:
        """
        response = data_gen_llm.complete(prompt)

        train_data.append({
            "query": query,
            "context": context,
            "response": str(response).strip()
        })

        if (i + 1) % 10 == 0 or (i + 1) == total_pairs:
            print(f"  已处理 {i + 1}/{total_pairs}...")
            
    except Exception as e:
        print(f"处理第 {i+1} 个问答对时出错: {e}")
        continue

# 保存为JSONL格式
try:
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        for item in train_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"--- 微调数据集构建成功！已保存至 {OUTPUT_FILE} ---")
    print(f"共生成 {len(train_data)} 条训练数据")
    
    # 显示一个示例
    if train_data:
        print("\n--- 数据示例 ---")
        example = train_data[0]
        print(f"问题: {example['query'][:100]}...")
        print(f"上下文: {example['context'][:100]}...")
        print(f"答案: {example['response'][:100]}...")
        
except Exception as e:
    print(f"保存文件时出错: {e}")
